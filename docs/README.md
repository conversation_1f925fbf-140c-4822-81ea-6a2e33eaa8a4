# h3z Official Website

This directory contains the official website for h3z framework, hosted on GitHub Pages.

## 🌐 Live Website

Visit the live website at: [https://dg0230.github.io/h3z](https://dg0230.github.io/h3z)

## 📁 Structure

```
docs/
├── index.html          # Main homepage
├── styles.css          # CSS styles
├── script.js           # JavaScript functionality
├── favicon.svg         # Website favicon
├── _config.yml         # GitHub Pages configuration
└── README.md           # This file
```

## 🚀 Features

- **Modern Design**: Clean, responsive design inspired by h3.dev
- **Interactive Elements**: Smooth scrolling, animations, and hover effects
- **Code Examples**: Syntax-highlighted Zig code examples
- **Mobile Responsive**: Optimized for all device sizes
- **Fast Loading**: Minimal dependencies and optimized assets

## 🎨 Design Highlights

- **Hero Section**: Eye-catching introduction with live code example
- **Features Grid**: Showcases h3z's key benefits
- **Quick Start Guide**: Step-by-step installation instructions
- **Responsive Layout**: Works perfectly on desktop, tablet, and mobile

## 🛠️ Local Development

To run the website locally:

1. Clone the repository
2. Navigate to the `docs` directory
3. Serve the files using any static server:

```bash
# Using Python
python -m http.server 8000

# Using Node.js (http-server)
npx http-server

# Using PHP
php -S localhost:8000
```

4. Open `http://localhost:8000` in your browser

## 📝 Content Updates

To update the website content:

1. Edit the HTML in `index.html`
2. Modify styles in `styles.css`
3. Update JavaScript functionality in `script.js`
4. Commit and push changes to the `main` branch
5. GitHub Pages will automatically deploy the updates

## 🔧 GitHub Pages Setup

The website is configured to deploy from the `/docs` folder on the `main` branch.

To enable GitHub Pages:

1. Go to repository Settings
2. Navigate to Pages section
3. Set Source to "Deploy from a branch"
4. Select branch: `main`
5. Select folder: `/docs`
6. Save the configuration

## 📊 Analytics

The website includes:
- SEO optimization with meta tags
- Open Graph tags for social sharing
- Structured data for search engines
- Performance optimizations

## 🎯 Performance

- **Lighthouse Score**: Optimized for 90+ scores
- **Core Web Vitals**: Fast loading and interaction
- **Accessibility**: WCAG compliant design
- **SEO**: Search engine optimized

## 🤝 Contributing

To contribute to the website:

1. Fork the repository
2. Create a feature branch
3. Make your changes in the `docs` directory
4. Test locally
5. Submit a pull request

## 📄 License

The website content is part of the h3z project and follows the same license terms.
