<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>h3z Code Highlighting Preview</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8fafc;
        }
        .preview-section {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            margin-bottom: 2rem;
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
        }
        h1, h2 {
            color: #0f172a;
            margin-bottom: 1rem;
        }
        .code-window {
            background: #1e293b;
            border-radius: 0.5rem;
            overflow: hidden;
            margin: 1rem 0;
        }
        .code-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 1.5rem;
            background: #334155;
            border-bottom: 1px solid #475569;
        }
        .code-dots {
            display: flex;
            gap: 0.5rem;
        }
        .dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }
        .dot.red { background: #ef4444; }
        .dot.yellow { background: #f59e0b; }
        .dot.green { background: #10b981; }
        .code-title {
            color: #e2e8f0;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .code-content {
            padding: 1.5rem;
        }
        .code-content pre {
            margin: 0;
            background: transparent !important;
        }
        .code-content code {
            background: transparent !important;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <h1>h3z Code Highlighting Preview</h1>
    
    <div class="preview-section">
        <h2>Zig Code Example</h2>
        <div class="code-window">
            <div class="code-header">
                <div class="code-dots">
                    <span class="dot red"></span>
                    <span class="dot yellow"></span>
                    <span class="dot green"></span>
                </div>
                <span class="code-title">main.zig</span>
            </div>
            <div class="code-content">
                <pre><code class="language-zig">const std = @import("std");
const h3 = @import("h3");

pub fn main() !void {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    
    var app = try h3.createApp(gpa.allocator());
    defer app.deinit();
    
    // Add middleware
    _ = app.use(h3.middleware.logger);
    _ = app.use(h3.middleware.cors);
    
    // Define routes
    _ = app.get("/", homeHandler);
    _ = app.get("/api/users/:id", getUserHandler);
    _ = app.post("/api/users", createUserHandler);
    
    // Start server
    try h3.serve(&app, .{ 
        .port = 3000,
        .host = "127.0.0.1"
    });
}

fn homeHandler(event: *h3.Event) !void {
    const html = 
        \\<!DOCTYPE html>
        \\<html>
        \\<head><title>h3z Server</title></head>
        \\<body>
        \\    <h1>Welcome to h3z!</h1>
        \\    <p>High-performance HTTP server for Zig</p>
        \\</body>
        \\</html>
    ;
    try h3.sendHtml(event, html);
}

fn getUserHandler(event: *h3.Event) !void {
    const id = h3.getParam(event, "id") orelse "unknown";
    const user = User{
        .id = try std.fmt.parseInt(u32, id, 10),
        .name = "Zig Developer",
        .email = "<EMAIL>",
        .active = true,
    };
    try h3.sendJson(event, user);
}

const User = struct {
    id: u32,
    name: []const u8,
    email: []const u8,
    active: bool,
};</code></pre>
            </div>
        </div>
    </div>
    
    <div class="preview-section">
        <h2>Bash Commands</h2>
        <div class="code-window">
            <div class="code-header">
                <div class="code-dots">
                    <span class="dot red"></span>
                    <span class="dot yellow"></span>
                    <span class="dot green"></span>
                </div>
                <span class="code-title">terminal</span>
            </div>
            <div class="code-content">
                <pre><code class="language-bash"># Install Zig
curl -sSL https://ziglang.org/download/ | sh

# Create new project
zig init

# Add h3z dependency to build.zig.zon
echo '.dependencies = .{ .h3 = .{ .url = "https://github.com/dg0230/h3z/archive/main.tar.gz" } }' >> build.zig.zon

# Build and run
zig build run

# Run tests
zig build test

# Build optimized release
zig build -Doptimize=ReleaseFast</code></pre>
            </div>
        </div>
    </div>
    
    <div class="preview-section">
        <h2>Build Configuration</h2>
        <div class="code-window">
            <div class="code-header">
                <div class="code-dots">
                    <span class="dot red"></span>
                    <span class="dot yellow"></span>
                    <span class="dot green"></span>
                </div>
                <span class="code-title">build.zig.zon</span>
            </div>
            <div class="code-content">
                <pre><code class="language-zig">.{
    .name = "my-h3z-app",
    .version = "0.1.0",
    .minimum_zig_version = "0.14.0",
    
    .dependencies = .{
        .h3 = .{
            .url = "https://github.com/dg0230/h3z/archive/main.tar.gz",
            .hash = "1234567890abcdef...", // zig will provide this
        },
    },
    
    .paths = .{
        "build.zig",
        "build.zig.zon",
        "src",
        "README.md",
    },
}</code></pre>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/copy-to-clipboard/prism-copy-to-clipboard.min.js"></script>
    <script>
        // Define Zig language for Prism.js
        Prism.languages.zig = {
            'comment': [
                {
                    pattern: /(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,
                    lookbehind: true
                },
                {
                    pattern: /(^|[^\\:])\/\/.*/,
                    lookbehind: true
                }
            ],
            'string': {
                pattern: /(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,
                greedy: true
            },
            'keyword': /\b(?:const|var|fn|pub|try|catch|if|else|while|for|switch|return|defer|errdefer|unreachable|break|continue|struct|enum|union|error|test|comptime|inline|export|extern|packed|align|volatile|allowzero|noalias)\b/,
            'builtin': /\b(?:u8|u16|u32|u64|u128|i8|i16|i32|i64|i128|f16|f32|f64|f128|bool|void|type|anytype|anyopaque|noreturn|c_short|c_ushort|c_int|c_uint|c_long|c_ulong|c_longlong|c_ulonglong|c_longdouble|c_void|comptime_int|comptime_float)\b/,
            'function': /\b[a-zA-Z_]\w*(?=\s*\()/,
            'number': /\b(?:0[xX][\da-fA-F]+(?:\.[\da-fA-F]*)?(?:[pP][+-]?\d+)?|\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)\b/,
            'boolean': /\b(?:true|false|null|undefined)\b/,
            'operator': /[+\-*\/%=!<>&|^~?:@]/,
            'punctuation': /[{}[\];(),.]/ 
        };
        
        Prism.highlightAll();
    </script>
</body>
</html>
