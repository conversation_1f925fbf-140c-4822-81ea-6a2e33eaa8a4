//! Simple concurrency tests for H3 framework
//! Basic thread safety verification without complex interactions

const std = @import("std");
const testing = std.testing;
const h3 = @import("h3");
const Thread = std.Thread;
const Atomic = std.atomic.Value;

const THREAD_COUNT = 4;
const OPERATIONS_PER_THREAD = 50;

// Test basic app creation in multiple threads
test "Simple Concurrency: App creation and destruction" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    var success_count = Atomic(u32).init(0);
    var error_count = Atomic(u32).init(0);

    const WorkerContext = struct {
        allocator: std.mem.Allocator,
        success: *Atomic(u32),
        errors: *Atomic(u32),

        fn worker(ctx: @This()) void {
            var local_success: u32 = 0;
            var local_errors: u32 = 0;

            for (0..OPERATIONS_PER_THREAD) |_| {
                // Create and destroy app
                var app = h3.createApp(ctx.allocator) catch {
                    local_errors += 1;
                    continue;
                };
                defer app.deinit();

                local_success += 1;

                // Small delay
                std.time.sleep(1000); // 1μs
            }

            _ = ctx.success.fetchAdd(local_success, .monotonic);
            _ = ctx.errors.fetchAdd(local_errors, .monotonic);
        }
    };

    var threads: [THREAD_COUNT]Thread = undefined;
    var contexts: [THREAD_COUNT]WorkerContext = undefined;

    for (0..THREAD_COUNT) |i| {
        contexts[i] = WorkerContext{
            .allocator = allocator,
            .success = &success_count,
            .errors = &error_count,
        };

        threads[i] = try Thread.spawn(.{}, WorkerContext.worker, .{contexts[i]});
    }

    // Wait for all threads
    for (0..THREAD_COUNT) |i| {
        threads[i].join();
    }

    const total_success = success_count.load(.monotonic);
    const total_errors = error_count.load(.monotonic);
    const expected_total = THREAD_COUNT * OPERATIONS_PER_THREAD;

    std.log.info("App creation test results:", .{});
    std.log.info("  Total operations: {d}", .{expected_total});
    std.log.info("  Successful: {d}", .{total_success});
    std.log.info("  Errors: {d}", .{total_errors});

    // Should have some successful operations
    try testing.expect(total_success > 0);
    try testing.expect(total_success + total_errors == expected_total);

    std.log.info("✅ App creation concurrency test passed!", .{});
}

// Test route registration without complex interactions
test "Simple Concurrency: Basic route registration" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    // Create a single app to test concurrent route registration
    var app = try h3.createApp(allocator);
    defer app.deinit();

    const testHandler = struct {
        fn handler(event: *h3.Event) !void {
            try event.sendText("OK");
        }
    }.handler;

    var routes_added = Atomic(u32).init(0);

    const RouteContext = struct {
        app: *h3.H3,
        thread_id: u32,
        routes_added: *Atomic(u32),

        fn routeWorker(ctx: @This()) void {
            var local_routes: u32 = 0;

            for (0..OPERATIONS_PER_THREAD / 10) |i| { // Fewer operations
                var path_buffer: [64]u8 = undefined;
                const path = std.fmt.bufPrint(path_buffer[0..], "/t{d}/r{d}", .{ ctx.thread_id, i }) catch continue;

                // Register route
                _ = ctx.app.get(path, testHandler);
                local_routes += 1;

                // Small delay
                std.time.sleep(10000); // 10μs
            }

            _ = ctx.routes_added.fetchAdd(local_routes, .monotonic);
        }
    };

    var threads: [THREAD_COUNT]Thread = undefined;
    var contexts: [THREAD_COUNT]RouteContext = undefined;

    for (0..THREAD_COUNT) |i| {
        contexts[i] = RouteContext{
            .app = &app,
            .thread_id = @intCast(i),
            .routes_added = &routes_added,
        };

        threads[i] = try Thread.spawn(.{}, RouteContext.routeWorker, .{contexts[i]});
    }

    // Wait for all threads
    for (0..THREAD_COUNT) |i| {
        threads[i].join();
    }

    const total_routes = routes_added.load(.monotonic);
    const expected_routes = THREAD_COUNT * (OPERATIONS_PER_THREAD / 10);

    std.log.info("Route registration test results:", .{});
    std.log.info("  Expected routes: {d}", .{expected_routes});
    std.log.info("  Routes added: {d}", .{total_routes});
    std.log.info("  App route count: {d}", .{app.getRouteCount()});

    try testing.expect(total_routes == expected_routes);
    try testing.expect(app.getRouteCount() > 0);

    std.log.info("✅ Route registration concurrency test passed!", .{});
}

// Test atomic counter operations
test "Simple Concurrency: Atomic operations" {
    var counter = Atomic(u64).init(0);

    const CounterContext = struct {
        counter: *Atomic(u64),

        fn counterWorker(ctx: @This()) void {
            for (0..OPERATIONS_PER_THREAD) |_| {
                _ = ctx.counter.fetchAdd(1, .monotonic);

                // Small delay
                std.time.sleep(100); // 0.1μs
            }
        }
    };

    var threads: [THREAD_COUNT]Thread = undefined;
    var contexts: [THREAD_COUNT]CounterContext = undefined;

    for (0..THREAD_COUNT) |i| {
        contexts[i] = CounterContext{
            .counter = &counter,
        };

        threads[i] = try Thread.spawn(.{}, CounterContext.counterWorker, .{contexts[i]});
    }

    // Wait for all threads
    for (0..THREAD_COUNT) |i| {
        threads[i].join();
    }

    const final_count = counter.load(.monotonic);
    const expected_count = THREAD_COUNT * OPERATIONS_PER_THREAD;

    std.log.info("Atomic operations test results:", .{});
    std.log.info("  Expected count: {d}", .{expected_count});
    std.log.info("  Final count: {d}", .{final_count});

    try testing.expectEqual(expected_count, final_count);

    std.log.info("✅ Atomic operations test passed!", .{});
}

// Test memory allocation under concurrent load
test "Simple Concurrency: Memory allocation stress" {
    var gpa = std.heap.GeneralPurposeAllocator(.{}){};
    defer _ = gpa.deinit();
    const allocator = gpa.allocator();

    var allocations_made = Atomic(u32).init(0);

    const MemoryContext = struct {
        allocator: std.mem.Allocator,
        allocations: *Atomic(u32),

        fn memoryWorker(ctx: @This()) void {
            var local_allocations: u32 = 0;

            for (0..OPERATIONS_PER_THREAD / 20) |_| { // Much fewer operations
                // Allocate and free memory
                const memory = ctx.allocator.alloc(u8, 1024) catch continue;
                defer ctx.allocator.free(memory);

                // Write to memory to ensure it's valid
                for (memory, 0..) |*byte, i| {
                    byte.* = @intCast(i % 256);
                }

                local_allocations += 1;

                // Small delay
                std.time.sleep(50000); // 50μs
            }

            _ = ctx.allocations.fetchAdd(local_allocations, .monotonic);
        }
    };

    var threads: [THREAD_COUNT]Thread = undefined;
    var contexts: [THREAD_COUNT]MemoryContext = undefined;

    for (0..THREAD_COUNT) |i| {
        contexts[i] = MemoryContext{
            .allocator = allocator,
            .allocations = &allocations_made,
        };

        threads[i] = try Thread.spawn(.{}, MemoryContext.memoryWorker, .{contexts[i]});
    }

    // Wait for all threads
    for (0..THREAD_COUNT) |i| {
        threads[i].join();
    }

    const total_allocations = allocations_made.load(.monotonic);
    const expected_allocations = THREAD_COUNT * (OPERATIONS_PER_THREAD / 20);

    std.log.info("Memory allocation test results:", .{});
    std.log.info("  Expected allocations: {d}", .{expected_allocations});
    std.log.info("  Actual allocations: {d}", .{total_allocations});

    try testing.expectEqual(expected_allocations, total_allocations);

    std.log.info("✅ Memory allocation stress test passed!", .{});
}

// Simple performance benchmark
test "Simple Concurrency: Performance benchmark" {
    const start_time = std.time.milliTimestamp();

    var operations_completed = Atomic(u64).init(0);

    const BenchmarkContext = struct {
        operations: *Atomic(u64),

        fn benchmarkWorker(ctx: @This()) void {
            for (0..OPERATIONS_PER_THREAD * 10) |_| { // More operations for benchmark
                // Simulate some work
                var sum: u64 = 0;
                for (0..100) |i| {
                    sum += i;
                }

                _ = ctx.operations.fetchAdd(1, .monotonic);
            }
        }
    };

    var threads: [THREAD_COUNT]Thread = undefined;
    var contexts: [THREAD_COUNT]BenchmarkContext = undefined;

    for (0..THREAD_COUNT) |i| {
        contexts[i] = BenchmarkContext{
            .operations = &operations_completed,
        };

        threads[i] = try Thread.spawn(.{}, BenchmarkContext.benchmarkWorker, .{contexts[i]});
    }

    // Wait for all threads
    for (0..THREAD_COUNT) |i| {
        threads[i].join();
    }

    const end_time = std.time.milliTimestamp();
    const total_operations = operations_completed.load(.monotonic);
    const total_time = end_time - start_time;
    const ops_per_second = @as(f64, @floatFromInt(total_operations)) /
        (@as(f64, @floatFromInt(total_time)) / 1000.0);

    std.log.info("Performance benchmark results:", .{});
    std.log.info("  Total operations: {d}", .{total_operations});
    std.log.info("  Total time: {d}ms", .{total_time});
    std.log.info("  Operations per second: {d:.2}", .{ops_per_second});

    try testing.expect(total_operations > 0);
    try testing.expect(ops_per_second > 1000.0); // Should be reasonably fast

    std.log.info("✅ Performance benchmark passed!", .{});
}
